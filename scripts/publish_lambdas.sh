#!/usr/bin/env bash

# This script will publish the latest version of the lambda functions to the specified S3 buckets.
# It will also set a `version-latest` file that points to the latest version of the lambda function.
#
# Usage:
#   ./publish_lambdas.sh [version-tag]
#
# If no version-tag is provided, it will default to "latest".

set -e

CODE_ROOT="$(dirname "${BASH_SOURCE[0]}")/.."

S3_BUCKETS=(
    "braintrust-assets-us-east-1"
    "braintrust-assets-us-east-2"
    "braintrust-assets-us-west-2"
    "braintrust-assets-eu-west-1"
)
if [ $# -eq 0 ]; then
    echo "Error: Version tag is required."
    echo "Usage: $0 <version-tag>"
    echo "Example: $0 v1.2.3"
    exit 1
fi
VERSION_TAG="$1"
PUBLISH_SUMMARY=""
PIDS=()

function publish_lambda() {
    local lambda_name="$1"
    local file_name="$2"
    local file_base_name; file_base_name=$(basename "${file_name}")
    local file_extension; file_extension=${file_base_name##*.}

    local md5; md5=$(md5sum "${file_name}" | awk '{print $1}')
    local s3_path="lambda/${lambda_name}/versions/${md5}.${file_extension}"

    for bucket in "${S3_BUCKETS[@]}"; do
        (
            echo "Publishing lambda ${lambda_name} to ${bucket}"
            if aws s3api head-object --bucket "${bucket}" --key "${s3_path}" > /dev/null 2>&1; then
                echo "SKIPPING ${file_name} (already exists) in ${bucket}."
            else
                echo "Publishing ${file_name} to ${bucket}/${s3_path}"
                aws s3 cp --no-progress "$file_name" "s3://${bucket}/${s3_path}"
            fi

            echo "Setting version-${VERSION_TAG} pointer in ${bucket}"
            echo "${s3_path}" > "version-${VERSION_TAG}-${bucket}"
            aws s3 cp --no-progress "version-${VERSION_TAG}-${bucket}" "s3://${bucket}/lambda/${lambda_name}/version-${VERSION_TAG}"
            rm "version-${VERSION_TAG}-${bucket}"
        ) &
        PIDS+=($!)
    done

    # Wait for all background processes for this lambda to complete
    for pid in "${PIDS[@]}"; do
        wait "$pid"
    done
    PIDS=()  # Reset PIDS array for next lambda

    # Build summary for this lambda
    PUBLISH_SUMMARY+="Lambda: ${lambda_name}\n"
    PUBLISH_SUMMARY+="Version Pointer: version-${VERSION_TAG}\n"
    PUBLISH_SUMMARY+="Published to:\n"
    for bucket in "${S3_BUCKETS[@]}"; do
        PUBLISH_SUMMARY+="  - s3://${bucket}/lambda/${lambda_name}/versions/${md5}.${file_extension}\n"
    done
    PUBLISH_SUMMARY+="\n"
}

echo "Checking AWS credentials..."
if ! aws sts get-caller-identity &>/dev/null; then
    echo "No valid AWS credentials found. Please run 'aws sso login' and try again."
    exit 1
fi

echo "Checking for required commands..."
commands=(
    "aws"
    "md5sum"
)
for cmd in "${commands[@]}"; do
    if ! command -v "${cmd}" &>/dev/null; then
        echo "Error: ${cmd} command not found. Please install it and try again."
        exit 1
    fi
done

echo "Publishing lambdas..."
publish_lambda "AIProxy" "${CODE_ROOT}/api-ts/dist/proxy/index.zip"
publish_lambda "APIHandler" "${CODE_ROOT}/api-ts/dist/lambda/index.zip"
publish_lambda "MigrateDatabaseFunction" "${CODE_ROOT}/api-schema/deployment.zip"
publish_lambda "QuarantineWarmupFunction" "${CODE_ROOT}/api-ts/dist/lambda-quarantine/warmup-lambda/index.zip"
publish_lambda "CatchupETL" "${CODE_ROOT}/api-ts/dist/lambda-etl/index.zip"
publish_lambda "AutomationCron" "${CODE_ROOT}/api-ts/dist/lambda-cron/index.zip"
publish_lambda "BillingCron" "${CODE_ROOT}/api-ts/dist/lambda-billing-cron/index.zip"

echo -e "Publish Summary:"
echo "=================="
echo -e "${PUBLISH_SUMMARY}"
