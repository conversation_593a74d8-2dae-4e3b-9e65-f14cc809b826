import { HotKeysHelper } from "#/ui/hotkeys";
import { Toaster } from "#/ui/toaster";
import { TooltipProvider } from "#/ui/tooltip";

import { HotkeysProvider } from "#/ui/hotkeys";
import { ThemeProvider } from "#/ui/theme-provider";
import { getUserContext } from "#/utils/user-context-action";
import { IncidentStatusProvider } from "#/utils/incident-status";
import { getIncidentStatus } from "#/utils/get-incident-status";
import { ClientLayout } from "../app/clientlayout";
import { redirect } from "next/navigation";
import { getNonce } from "#/security/csp";
import { EnvNotice } from "./env-notice";
const INITIALLY_ACTIVE_SCOPES = ["global"];

export default async function layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const userContext = await getUserContext({});
  const incidentStatus = await getIncidentStatus();

  if (!userContext.isAdmin) {
    return redirect("/app");
  }

  return (
    <div className="flex min-h-full w-full flex-col">
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
        nonce={await getNonce()}
      >
        <TooltipProvider>
          <HotkeysProvider initiallyActiveScopes={INITIALLY_ACTIVE_SCOPES}>
            <IncidentStatusProvider value={incidentStatus || undefined}>
              <HotKeysHelper>
                <ClientLayout userContext={userContext}>
                  {children}
                </ClientLayout>
              </HotKeysHelper>
              <Toaster />
              <EnvNotice />
            </IncidentStatusProvider>
          </HotkeysProvider>
        </TooltipProvider>
      </ThemeProvider>
    </div>
  );
}
