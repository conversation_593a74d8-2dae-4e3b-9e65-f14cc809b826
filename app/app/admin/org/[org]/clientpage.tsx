"use client";

import { runEnableBackfilling } from "#/app/app/[org]/p/[project]/brainstore/ops";
import { useBrainstoreProjectStatus } from "#/app/app/[org]/p/[project]/configuration/brainstore/brainstore-project-configuration";
import { getLabelForPlanId } from "#/app/app/[org]/settings/billing/plans";
import {
  type APIVersionInfo,
  apiVersionSchema,
} from "#/ui/api-version/check-api-version";
import { type FormatterProps, Table } from "#/ui/arrow-table";
import { Button } from "#/ui/button";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { Spinner } from "#/ui/icons/spinner";
import { PlainInput } from "#/ui/plain-input";
import { makeFormatterMap } from "#/ui/table/formatters/header-formatters";
import { type BT_IS_GROUP } from "#/ui/table/grouping/queries";
import { TableSkeleton } from "#/ui/table/table-skeleton";
import { useSessionToken } from "#/utils/auth/session-token";
import { apiFetchGet } from "#/utils/btapi/fetch";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { isEmpty } from "#/utils/object";
import { useQueryFunc } from "#/utils/react-query";
import { useIsClient } from "#/utils/use-is-client";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { MultiTenantApiURL } from "#/utils/user-types";
import { useViewStates } from "#/utils/view/use-view";
import { useAuth } from "@clerk/nextjs";
import { type Row } from "@tanstack/react-table";
import { Field, Schema, Utf8 } from "apache-arrow";
import Fuse from "fuse.js";
import {
  CheckCircle2,
  Clipboard,
  HelpCircle,
  Search,
  Trash,
} from "lucide-react";
import { useRouter } from "next/navigation";
import {
  type MouseEventHandler,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { toast } from "sonner";
import { z } from "zod";
import {
  type adminArchiveOrg,
  type adminCreateBrainstoreLicense,
  type adminFetchAllOrgs,
  type adminFetchAllUsers,
  type adminFetchResourceCounts,
  type OrgRow,
  type ProjectRow,
  type UserRow,
} from "../../actions";
import { makeOrgLink } from "../../clientpage";
import { ResourceLimits } from "./ResourceLimits";
import { TelemetryUrlInput } from "./telemetry-url-input";
import { type adminGetOrbCustomerPortal } from "#/app/admin/actions";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";
import { BillingStatus } from "./billing-status";
import { ONE_KIB, ONE_MIB } from "./resources";
import { ONE_GIB } from "./resources";
import { Input } from "#/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
const projectSchema = new Schema([
  Field.new({ name: "id", type: new Utf8() }),
  Field.new({ name: "name", type: new Utf8() }),
  Field.new({ name: "created", type: new Utf8() }),
  Field.new({ name: "brainstore", type: new Utf8() }),
]);

const userSchema = new Schema([
  Field.new({ name: "id", type: new Utf8() }),
  Field.new({ name: "given_name", type: new Utf8() }),
  Field.new({ name: "family_name", type: new Utf8() }),
  Field.new({ name: "email", type: new Utf8() }),
  Field.new({ name: "created", type: new Utf8() }),
  Field.new({ name: "groups", type: new Utf8() }),
]);

const brainstoreVersionInfoSchema = z.object({
  commit: z.string(),
});
type BrainstoreVersionInfo = z.infer<typeof brainstoreVersionInfoSchema>;

function makeProjectLink(org: string, name: string) {
  return `/admin/org/${encodeURIComponent(org)}/${encodeURIComponent(name)}`;
}

export function ClientPage({
  projectRows: projectRowsProp,
  orgInfo: orgInfoProp,
}: {
  projectRows: ProjectRow[];
  orgInfo: OrgRow[];
}) {
  const { data: orgInfoRows, invalidate: refreshOrgRows } = useQueryFunc<
    typeof adminFetchAllOrgs
  >({
    fName: "adminFetchAllOrgs",
    args: {
      name: orgInfoProp[0].name,
    },
    serverData: orgInfoProp,
  });
  const orgInfo = useMemo(() => orgInfoRows?.[0], [orgInfoRows]);

  const { data: userRows } = useQueryFunc<typeof adminFetchAllUsers>({
    fName: "adminFetchAllUsers",
    args: {
      orgName: orgInfo.name,
    },
  });

  const [search, setSearch] = useState("");
  const viewProps = useViewStates({
    pageIdentifier: "admin-org",
    viewParams: undefined,
    clauseChecker: null,
  });

  const isClient = useIsClient();

  const setSearchDebounced = useDebouncedCallback(setSearch, 200);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const projectRows = useMemo(() => {
    return projectRowsProp.map((project) => ({
      ...project,
      brainstore: null,
      api_url: orgInfo.api_url,
    }));
  }, [projectRowsProp, orgInfo.api_url]);

  const fuse = useMemo(
    () =>
      new Fuse(projectRows, {
        keys: ["name", "id"],
        threshold: 0.0,
        ignoreLocation: true,
        includeMatches: true,
      }),
    [projectRows],
  );
  const filteredProjects = useMemo(() => {
    if (!search) return projectRows;
    const results = fuse.search(search);
    return results.map((result) => result.item);
  }, [search, fuse, projectRows]);

  const router = useRouter();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const rowEvents: Record<string, (row: Row<any>) => MouseEventHandler> =
    useMemo(() => {
      return {
        onClick: (row) => (e) => {
          if (e.ctrlKey || e.metaKey || e.button === 1) {
            window.open(
              makeProjectLink(orgInfo.name, row.original.name),
              "_blank",
            );
          } else {
            router.push(makeProjectLink(orgInfo.name, row.original.name));
          }
        },
        onAuxClick: (row) => () => {
          window.open(
            makeProjectLink(orgInfo.name, row.original.name),
            "_blank",
          );
        },
      };
    }, [router, orgInfo.name]);

  const columnFilters = useMemo(() => [], []);
  const setColumnFilters = useCallback(() => {}, []);

  const formatters = useMemo(() => {
    return makeFormatterMap<ProjectRow & { api_url: string | null }, string>({
      brainstore: {
        cell: BackfillStatusFormatter,
      },
    });
  }, []);

  const userFormatters = useMemo(() => {
    return makeFormatterMap<UserRow, string>({
      groups: {
        cell: GroupsFormatter,
      },
    });
  }, []);

  const { getToken } = useAuth();
  const [isCreatingLicense, setIsCreatingLicense] = useState(false);
  const createBrainstoreLicense = useCallback(async () => {
    setIsCreatingLicense(true);
    try {
      await invokeServerAction<typeof adminCreateBrainstoreLicense>({
        fName: "adminCreateBrainstoreLicense",
        args: {
          orgId: orgInfo.id,
        },
        getToken,
      });
      await refreshOrgRows();
    } finally {
      setIsCreatingLicense(false);
    }
  }, [getToken, orgInfo.id, refreshOrgRows]);

  const { getOrRefreshToken } = useSessionToken();

  const [versionInfo, setVersionInfo] = useState<APIVersionInfo | null>(null);
  const fetchedVersionInfo = useRef(false);
  useEffect(() => {
    const fetchVersionInfo = async () => {
      if (fetchedVersionInfo.current) {
        return;
      }
      fetchedVersionInfo.current = true;
      const data = await apiFetchGet(
        `${orgInfo.api_url ?? MultiTenantApiURL}/version`,
        await getOrRefreshToken(),
      );
      const json = await data.json();
      setVersionInfo(apiVersionSchema.parse(json));
    };
    fetchVersionInfo();
  }, [getOrRefreshToken, orgInfo.api_url, versionInfo]);

  const [brainstoreVersionInfo, setBrainstoreVersionInfo] =
    useState<BrainstoreVersionInfo | null>(null);
  const fetchedBrainstoreVersionInfo = useRef(false);
  useEffect(() => {
    const fetchBrainstoreVersionInfo = async () => {
      if (fetchedBrainstoreVersionInfo.current) {
        return;
      }
      fetchedBrainstoreVersionInfo.current = true;
      const data = await apiFetchGet(
        `${orgInfo.api_url ?? MultiTenantApiURL}/brainstore/version`,
        await getOrRefreshToken(),
      );
      const json = await data.json();
      setBrainstoreVersionInfo(brainstoreVersionInfoSchema.parse(json));
    };
    fetchBrainstoreVersionInfo();
  }, [getOrRefreshToken, orgInfo.api_url]);

  const [isEnablingBackfilling, setIsEnablingBackfilling] = useState(false);
  const enableAllBackfilling = useCallback(async () => {
    setIsEnablingBackfilling(true);
    try {
      await runEnableBackfilling({
        apiUrl: orgInfo.api_url ?? MultiTenantApiURL,
        projectId: projectRows.map((project) => project.id),
        objectTypes: undefined,
        sessionToken: await getOrRefreshToken(),
      });
    } finally {
      setIsEnablingBackfilling(false);
    }
  }, [getOrRefreshToken, orgInfo.api_url, projectRows]);

  const [activeTab, setActiveTab] = useState<"projects" | "users">("projects");
  const [isArchiveModalOpen, setIsArchiveModalOpen] = useState(false);

  if (!isClient) {
    return <TableSkeleton />;
  }

  return (
    <div ref={scrollContainerRef} className="flex-1 overflow-auto">
      <div className="flex-1 overflow-auto px-5 pb-5">
        {/* Two-column layout container */}
        <div className="flex flex-col gap-6 lg:flex-row">
          <div className="mb-4 min-w-0 flex-1">
            <div className="flex flex-col">
              <div className="mb-2 text-sm text-primary-500">Org id</div>
              <div className="mb-2 text-sm">
                <Button
                  onClick={(e) => {
                    e.preventDefault();
                    navigator.clipboard.writeText(orgInfo.id ?? "");
                    toast("Organization ID copied to clipboard");
                  }}
                  size="inline"
                  className="border-0 text-xs font-normal text-primary-500"
                  IconRight={Clipboard}
                >
                  {orgInfo.id}
                </Button>
              </div>
            </div>

            {/* Tabs */}
            <div className="mb-4">
              <div className="border-b border-primary-200">
                <div className="flex space-x-6">
                  <button
                    className={`pb-2 text-sm font-medium ${activeTab === "projects" ? "border-b-2 border-primary-500 text-primary-900" : "text-primary-500 hover:text-primary-700"}`}
                    onClick={() => setActiveTab("projects")}
                  >
                    Projects
                  </button>
                  <button
                    className={`pb-2 text-sm font-medium ${activeTab === "users" ? "border-b-2 border-primary-500 text-primary-900" : "text-primary-500 hover:text-primary-700"}`}
                    onClick={() => setActiveTab("users")}
                  >
                    Users
                  </button>
                </div>
              </div>
            </div>

            {/* Tab Content */}
            <div className="mt-4">
              {/* Projects Tab */}
              {activeTab === "projects" && (
                <div className="flex-1 overflow-auto">
                  <Table
                    data={filteredProjects}
                    fields={projectSchema.fields}
                    columnFilters={columnFilters}
                    setColumnFilters={setColumnFilters}
                    viewProps={viewProps}
                    scrollContainerRef={scrollContainerRef}
                    rowEvents={rowEvents}
                    extraRightControls={
                      <>
                        <div className="relative flex flex-1">
                          <Search className="pointer-events-none absolute left-2 top-[8px] size-3 text-primary-500" />
                          <PlainInput
                            placeholder="Find project"
                            onChange={(e) => setSearchDebounced(e.target.value)}
                            className="h-7 flex-1 border-0 pl-7 outline-none transition-all"
                          />
                        </div>
                      </>
                    }
                    formatters={formatters}
                    tableType="list"
                  />
                </div>
              )}

              {/* Users Tab */}
              {activeTab === "users" && (
                <div className="flex-1 overflow-auto">
                  <Table
                    data={userRows || []}
                    fields={userSchema.fields}
                    columnFilters={[]}
                    setColumnFilters={() => {}}
                    viewProps={viewProps}
                    scrollContainerRef={scrollContainerRef}
                    tableType="list"
                    formatters={userFormatters}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Right column: Brainstore and Billing sections */}
          <div className="w-full flex-none space-y-4 lg:w-[350px]">
            <CollapsibleSection
              title="Hosting"
              defaultCollapsed={false}
              localStorageKey="admin-hosting-section"
            >
              <div className="flex flex-col">
                <div className="flex flex-row gap-8">
                  <div className="flex max-h-[160px] min-w-[200px] flex-col gap-1.5 overflow-y-auto">
                    <div className="text-xs">
                      {orgInfo.api_url &&
                      orgInfo.api_url !== MultiTenantApiURL ? (
                        <>Self-hosted ({orgInfo.api_url})</>
                      ) : (
                        "Braintrust-hosted"
                      )}
                    </div>
                    {versionInfo ? (
                      <div className="text-xs">
                        {Object.entries(versionInfo).map(([key, value]) => (
                          <div key={key}>
                            {key}: {`${value ?? "null"}`}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <Spinner className="size-3" />
                    )}
                  </div>
                </div>
              </div>
            </CollapsibleSection>

            {/* Brainstore Section */}
            <CollapsibleSection
              title="Brainstore"
              defaultCollapsed={false}
              localStorageKey="admin-brainstore-section"
            >
              <div className="space-y-4">
                {/* Brainstore License */}
                <div>
                  <div className="mb-2 text-xs text-primary-500">License</div>
                  <div className="mb-2 text-sm">
                    {isEmpty(orgInfo.brainstore_license_id) ? (
                      <Button size="xs" onClick={createBrainstoreLicense}>
                        Create license
                        {isCreatingLicense ? (
                          <Spinner className="ml-2 size-2" />
                        ) : null}
                      </Button>
                    ) : (
                      <div className="p-1.5 text-xs">
                        <span className="text-primary-700">Licensed</span>
                        <span className="inline-flex translate-y-[2px] items-center gap-1 rounded-full px-1 text-xs font-medium text-good-600">
                          <CheckCircle2 className="size-3" />
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Brainstore Backfill */}
                <div>
                  <div className="mb-2 text-xs text-primary-500">Backfill</div>
                  <div className="mb-2 text-sm">
                    <Button size="xs" onClick={enableAllBackfilling}>
                      Enable all
                      {isEnablingBackfilling ? (
                        <Spinner className="ml-2 size-2" />
                      ) : null}
                    </Button>
                  </div>
                </div>

                {/* Brainstore Processes */}
                {
                  <div className="flex flex-row gap-8">
                    <div className="flex flex-col">
                      <div className="mb-2 text-xs text-primary-500">
                        Processes
                      </div>
                      <div className="mb-2 text-sm">
                        <Button
                          onClick={() => {
                            router.push(
                              `${makeOrgLink(orgInfo.name)}/processes`,
                            );
                          }}
                          size="xs"
                        >
                          Open
                        </Button>
                      </div>
                    </div>
                    <div className="flex flex-col">
                      <div className="mb-2 text-xs text-primary-500">
                        Failed segments
                      </div>
                      <div className="mb-2 text-sm">
                        <Button
                          onClick={() => {
                            router.push(
                              `${makeOrgLink(orgInfo.name)}/failed-segments`,
                            );
                          }}
                          size="xs"
                        >
                          Open
                        </Button>
                      </div>
                    </div>
                    <div className="flex flex-col">
                      <div className="mb-2 text-xs text-primary-500">
                        System status
                      </div>
                      <div className="mb-2 text-sm">
                        <Button
                          onClick={() => {
                            router.push(`${makeOrgLink(orgInfo.name)}/status`);
                          }}
                          size="xs"
                        >
                          Open
                        </Button>
                      </div>
                    </div>
                  </div>
                }

                {brainstoreVersionInfo && (
                  <>
                    <div className="text-xs">
                      {Object.entries(brainstoreVersionInfo).map(
                        ([key, value]) => (
                          <div key={key}>
                            brainstore {key}: {`${value ?? "null"}`}
                          </div>
                        ),
                      )}
                    </div>
                  </>
                )}
              </div>
            </CollapsibleSection>

            {/* Billing Section */}
            <CollapsibleSection
              title="Billing"
              defaultCollapsed={false}
              localStorageKey="admin-billing-section"
            >
              <div className="space-y-4">
                {/* Plan */}
                <div className="mb-2 text-xs text-primary-500">Plan</div>
                <div className="flex items-center">
                  <div className="rounded-md border px-3 py-1.5 text-sm border-primary-200">
                    {getLabelForPlanId(orgInfo.plan_id ?? "") ||
                      "Not configured"}
                  </div>
                  <Button
                    onClick={(e) => {
                      e.preventDefault();
                      navigator.clipboard.writeText(orgInfo.plan_id ?? "");
                      toast("Plan ID copied to clipboard");
                    }}
                    size="sm"
                    variant="ghost"
                    className="ml-2"
                    IconRight={Clipboard}
                  >
                    Copy ID
                  </Button>
                </div>

                {/* Resource Limits */}
                <ResourceLimits
                  spanLimitValue={orgInfo.span_limit}
                  logBytesLimitValue={orgInfo.log_bytes_limit}
                  orgId={orgInfo.id}
                  refreshOrgRows={refreshOrgRows}
                />

                {/* Resource Counts */}
                <ResourceCounts
                  orgId={orgInfo.id}
                  isSelfHosted={
                    !!orgInfo.api_url && orgInfo.api_url !== MultiTenantApiURL
                  }
                  isUnlimited={
                    !!orgInfo.log_bytes_limit && !!orgInfo.span_limit
                  }
                  planId={orgInfo.plan_id}
                />

                {/* Telemetry URL */}
                <TelemetryUrlInput
                  orgId={orgInfo.id}
                  telemetryUrl={orgInfo.telemetry_url}
                  refreshOrgRows={refreshOrgRows}
                />

                <BillingStatus
                  orgId={orgInfo.id}
                  apiUrl={orgInfo.api_url ?? MultiTenantApiURL}
                  refreshOrgRows={refreshOrgRows}
                />
              </div>
            </CollapsibleSection>
            <CollapsibleSection title="Administer" defaultCollapsed={true}>
              <div className="flex flex-col gap-4">
                <div className="flex flex-col gap-4">
                  <Button
                    size="xs"
                    onClick={() => setIsArchiveModalOpen(true)}
                    IconLeft={Trash}
                  >
                    Archive this organization
                  </Button>
                </div>
              </div>
              {isArchiveModalOpen && (
                <ArchiveModal
                  open={isArchiveModalOpen}
                  orgId={orgInfo.id}
                  orgName={orgInfo.name}
                  onOpenChange={setIsArchiveModalOpen}
                />
              )}
            </CollapsibleSection>
          </div>
        </div>
      </div>
    </div>
  );
}

function ResourceCounts({
  orgId,
  isSelfHosted,
  isUnlimited,
  planId,
}: {
  orgId: string;
  isSelfHosted: boolean;
  isUnlimited: boolean;
  planId?: string | null;
}) {
  const { data: resourceCounts } = useQueryFunc<
    typeof adminFetchResourceCounts
  >({
    fName: "adminFetchResourceCounts",
    args: { orgId },
  });

  const { getToken } = useAuth();

  const handleCustomerPortalAccess = async () => {
    try {
      const result = await invokeServerAction<typeof adminGetOrbCustomerPortal>(
        {
          fName: "adminGetOrbCustomerPortal",
          args: { orgId },
          getToken,
        },
      );

      if (result.url) {
        window.open(result.url, "_blank");
      } else {
        toast("No portal URL returned");
      }
    } catch (error) {
      console.error("Error accessing customer portal:", error);
      toast("Failed to open Orb billing portal");
    }
  };

  if (!resourceCounts) {
    return <TableSkeleton />;
  }

  // Format large numbers with appropriate units
  const formatResourceValue = (resourceName: string, value: number): string => {
    // For log bytes, format with appropriate size units
    if (value >= ONE_GIB) {
      return `${(value / ONE_GIB).toFixed(2)} ${resourceName.includes("bytes") ? "GBs" : "bils"}`;
    } else if (value >= ONE_MIB) {
      return `${(value / ONE_MIB).toFixed(2)} ${resourceName.includes("bytes") ? "MBs" : "mils"}`;
    } else if (value >= ONE_KIB) {
      return `${(value / ONE_KIB).toFixed(2)} ${resourceName.includes("bytes") ? "KBs" : "thous"}`;
    }
    return value.toLocaleString();
  };

  const helpText = [
    ...(function* () {
      if (!isSelfHosted) {
        yield "Use Orb for metrics.";
      } else {
        yield "Orb may not yet have this organization.";
      }
      if (isUnlimited) {
        yield "Resource count data shown is small hourly sample.";
      }
    })(),
  ].join("\n\n");

  return (
    <>
      <div className="text-xs font-medium text-primary-500">
        Current Resource Usage{" "}
        <span className="text-primary-400">
          (
          {resourceCounts[0]
            ? new Date(resourceCounts[0]?.date_bucket).toDateString()
            : "N/A"}
          )
        </span>
        {isSelfHosted || isUnlimited ? (
          <Popover>
            <PopoverTrigger asChild>
              <HelpCircle className="ml-1 inline size-3 cursor-help text-primary-400" />
            </PopoverTrigger>
            <PopoverContent className="w-72">
              <p className="whitespace-pre-wrap text-xs">{helpText}</p>
            </PopoverContent>
          </Popover>
        ) : null}
        {!isSelfHosted && planId ? (
          <div className="mt-2">
            <Button
              onClick={handleCustomerPortalAccess}
              size="xs"
              className="ml-2"
            >
              Open Billing Portal
            </Button>
          </div>
        ) : null}
      </div>
      <div className="mt-2 grid gap-2">
        {resourceCounts.map((row, i) => (
          <div
            key={i}
            className="grid grid-cols-[1fr,auto] items-center gap-4 rounded-md px-3 py-2 bg-primary-50"
          >
            <div className="truncate text-sm" title={row.resource_name}>
              {row.resource_name}
            </div>
            <div className="text-right text-sm font-medium">
              {formatResourceValue(row.resource_name, row.count)}
            </div>
          </div>
        ))}
      </div>
    </>
  );
}

// Define types for the formatters
type Group = {
  id: string;
  name: string;
};

// Formatter for displaying groups in a hover/popup format
export function GroupsFormatter<TsTable extends UserRow, TsValue>({
  cell,
}: FormatterProps<TsTable, TsValue>) {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const groups = cell.getValue() as unknown as Group[];

  const value = groups.map((group) => group.name).join(", ");

  return <span title={value}>{value}</span>;
}

export function BackfillStatusFormatter<
  TsTable extends ProjectRow & {
    api_url: string | null;
    [BT_IS_GROUP]?: boolean;
  },
  TsValue,
>({ cell }: FormatterProps<TsTable, TsValue>) {
  const projectId = cell.row.original.id;
  const apiUrl = cell.row.original.api_url;

  const { data, error, requestErrorCode } = useBrainstoreProjectStatus({
    projectId,
    apiUrl: apiUrl ?? undefined,
  });

  interface ObjectStatus {
    last_backfilled_ts: string | null;
    estimated_progress: number | null;
  }

  const nonEmptyStatuses = data?.object_statuses?.filter(
    (status: ObjectStatus) => !isEmpty(status.last_backfilled_ts),
  );

  const minProgress = isEmpty(nonEmptyStatuses)
    ? null
    : nonEmptyStatuses.length > 0
      ? nonEmptyStatuses.reduce(
          (min: number, status: ObjectStatus) =>
            Math.min(
              min,
              (status.last_backfilled_ts ? status.estimated_progress : null) ??
                min,
            ),
          1,
        )
      : 0;

  return requestErrorCode === 403 ? (
    <div>Unauthorized</div>
  ) : !isEmpty(error) ? (
    <div className="bg-rose-500/5 border-rose-500/10 text-bad-700">
      {error instanceof Error ? error.message : error}
    </div>
  ) : isEmpty(data) ? (
    <div>
      <Spinner className="size-3" />
    </div>
  ) : isEmpty(data.object_statuses) ? (
    <div>Not enabled</div>
  ) : isEmpty(minProgress) ? (
    <div>May be empty</div>
  ) : (
    <div>Backfilled {Math.round((minProgress ?? 0) * 10000) / 100}%</div>
  );
}

function ArchiveModal({
  orgId,
  orgName,
  open,
  onOpenChange,
}: {
  orgId: string;
  orgName: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const { getToken } = useAuth();
  const router = useRouter();
  const [confirmationInput, setConfirmationInput] = useState("");

  // Detect if we're in production environment
  const isProduction =
    typeof window !== "undefined" &&
    window.location.hostname.includes("braintrust.dev");
  const environment = isProduction ? "production" : "development";

  const isConfirmationValid = confirmationInput.trim() === orgName;

  // Reset confirmation input when modal opens/closes
  useEffect(() => {
    if (open) {
      setConfirmationInput("");
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[540px]">
        <DialogHeader>
          <DialogTitle>Archive organization</DialogTitle>
          <DialogDescription asChild>
            <div className="space-y-4">
              <p>
                Are you sure you want to archive this organization? This action
                cannot be undone.
              </p>
              <div
                className={`rounded-md border p-3 ${
                  isProduction
                    ? "bg-yellow-50 border-yellow-200"
                    : "bg-blue-50 border-blue-200"
                }`}
              >
                <p
                  className={`text-sm ${
                    isProduction ? "text-yellow-800" : "text-blue-800"
                  }`}
                >
                  You are currently in the <strong>{environment}</strong>{" "}
                  environment.
                </p>
              </div>
              <div className="space-y-2">
                <p className="text-sm">
                  Please type <strong>{orgName}</strong> to confirm:
                </p>
                <Input
                  value={confirmationInput}
                  onChange={(e) => setConfirmationInput(e.target.value)}
                  placeholder={orgName}
                  className="w-full"
                  autoFocus
                />
              </div>
            </div>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button size="sm" variant="ghost" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            size="sm"
            variant="primary"
            disabled={!isConfirmationValid}
            onClick={async () => {
              if (!isConfirmationValid) return;

              const { archivedName } = await invokeServerAction<
                typeof adminArchiveOrg
              >({
                fName: "adminArchiveOrg",
                args: { orgId },
                getToken,
              });
              const newUrl = makeOrgLink(archivedName);
              router.push(newUrl);
              onOpenChange(false);
            }}
          >
            Archive
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
