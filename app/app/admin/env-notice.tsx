"use client";

import { AlertTriangle, InfoIcon } from "lucide-react";

export const EnvNotice = () => {
  if (typeof window === "undefined") {
    return null;
  }

  const isProduction = window?.location?.href?.includes("braintrust.dev");

  return (
    <div
      className={`flex items-center gap-2 rounded-lg px-3 py-2 text-xs font-medium shadow-lg ${
        isProduction
          ? "border bg-amber-100 border-amber-200 text-amber-800"
          : "border bg-blue-50 border-blue-200 text-blue-800"
      }`}
      style={{
        position: "fixed",
        bottom: "2rem",
        left: "1rem",
        zIndex: 50,
      }}
    >
      {isProduction ? (
        <AlertTriangle className="size-3" />
      ) : (
        <InfoIcon className="size-3" />
      )}
      {isProduction ? "Production" : "Development"}
    </div>
  );
};
