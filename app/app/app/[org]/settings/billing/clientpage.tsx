"use client";

import type Orb from "orb-billing";
import { PricingTable } from "./pricing-table";
import { CurrentSubscriptionSummary } from "./current-subscription-summary";
import { type Permission } from "@braintrust/core/typespecs";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { isFreeSubscription, isUnlimitedOrg } from "./plans";
import { useOrg } from "#/utils/user";
import { isFriendOrFamily } from "#/ui/unlimited-free-orgs";

function ClientPage({
  orgName,
  currentSubscription,
  paymentMethodSummary,
  invoices,
  orgPermissions,
}: {
  orgName: string;
  currentSubscription: Orb.Subscription | null;
  paymentMethodSummary: {
    last4: string;
    brand: string;
    exp_month: number;
    exp_year: number;
  } | null;
  invoices: Orb.Invoice[] | null;
  orgPermissions: Permission[];
}) {
  const org = useOrg();

  const isAllowedToPay = orgPermissions.includes("update");
  if (!isAllowedToPay) {
    return (
      <TableEmptyState label='You do not have permission to manage billing for this organization. Ask your administrator to grant the "Manage settings" permission for this organization.' />
    );
  }

  // Until we have all orgs in Orb, we hide the billing UI if the org has unlimited spans and doesn't have an Orb plan
  const isUnlimitedWithNoPlan =
    org?.api_url && isUnlimitedOrg({ org }) && org?.plan_id == null;

  const isFriend = isFriendOrFamily(org?.id ?? "");
  const shouldHideUsage = isUnlimitedWithNoPlan && !isFriend;

  if (shouldHideUsage) {
    return (
      <div className="mb-12">
        <h2 className="mb-6 text-lg font-semibold">Current plan</h2>
        <div className="rounded-md border p-6">
          Usage for this organization is coming soon
        </div>
      </div>
    );
  }

  const isSubscribedToFreePlan =
    currentSubscription && isFreeSubscription(currentSubscription);

  if (currentSubscription && !isSubscribedToFreePlan) {
    return (
      <CurrentSubscriptionSummary
        currentSubscription={currentSubscription}
        paymentMethodSummary={paymentMethodSummary}
        invoices={invoices}
      />
    );
  }

  return (
    <>
      {isSubscribedToFreePlan && (
        <CurrentSubscriptionSummary
          currentSubscription={currentSubscription}
          paymentMethodSummary={paymentMethodSummary}
          invoices={invoices}
        />
      )}
      <h2 className="text-lg font-semibold">All plans</h2>
      <p className="mb-6 text-sm text-primary-600">
        Select a billing plan for this organization
      </p>
      <PricingTable orgName={orgName} />
    </>
  );
}

export interface Params {
  org: string;
}

export { ClientPage };
